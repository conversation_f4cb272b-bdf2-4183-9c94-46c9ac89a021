class QuickViewFiltersController < AuthenticatedController
  include BaseTicketsHelper
  before_action :set_quick_view

  FILTER_MAP = {
    'Status' => :statuses,
    'Priority' => :priorities,
    'Source' => :source,
    'Assignment' => :assignment,
    'Assigned to' => [:assigned_to_ids, :assigned_to_names],
    'Created by' => [:created_by_ids, :assigned_to_names],
    'Timeframe' => :date_filter,
    'Workspace' => :workspace_id
  }

  def set_quick_view
    debugger
    puts "=== QUICK VIEW FILTERS DEBUG ==="
    puts "current_user: #{current_user&.id}"
    puts "scoped_company_user: #{scoped_company_user&.id}"
    puts "scoped_workspace: #{scoped_workspace&.id rescue 'ERROR'}"
    puts "loose_scoping: #{loose_scoping}"
    puts "=== END QUICK VIEW FILTERS DEBUG ==="

    @quick_view ||= scoped_company_user.quick_view_filters&.find_by(workspace_id: scoped_workspace.id)
  end

  def index
    if @quick_view.present?
      quick_view_filters_data = @quick_view.filters_data
    else
      quick_view_filters_data = scoped_workspace.quick_view_filters.find_by(company_user_id: nil).filters_data
    end

    # TODO: Need load testing before using this ticket count feature
    # quick_view_filters_data = quick_view_filters_data.map do |filters|
    #   filters['ticket_count'] = fetch_tickets_count(filters)
    #   filters
    # end

    render json: { filters_data: quick_view_filters_data.sort_by { |filter| filter['order'] } }, status: :ok
  end

  def update
    params_filter = params['quick_view_filter']

    if @quick_view.present? && @quick_view.filters_data.present?
      is_filter_updated = false
      filters_data = @quick_view.filters_data.map do |filter|
        if filter['id'] == params_filter['id']
          is_filter_updated = true
          params_filter
        else
          filter
        end
      end
      if (!is_filter_updated)
        filters_data << params_filter
      end
    else
      default_quick_views = QuickViewFilter.find_by(workspace_id: scoped_workspace.id, company_user_id: nil);
      filters_data = [params_filter] + default_quick_views.filters_data
    end
    set_quick_views_data(filters_data)
    if @quick_view.save
      render json: { message: "Quick view updated successfully." }, status: :ok
    else
      render json: { message: "Sorry, there was an error updating quick view." }, status: :unprocessable_entity
    end
  end
  
  def destroy
    if @quick_view.present?
      filters = @quick_view.filters_data
      filters = filters.reject { |filter| filter['title'] == params['title'] && filter['id'].to_s == params['id'] }
      if @quick_view.update_columns(filters_data: filters)
        return render json: {}, status: :ok
      end
    end

    render json: {}, status: :not_found
  end

  def reorder
    filters = params['quick_filters']
    if @quick_view.present?
      @quick_view.filters_data = filters
    else
      @quick_view = scoped_company_user.quick_view_filters.new(workspace_id: scoped_workspace.id, filters_data: filters, company_id: scoped_company.id)
    end

    if @quick_view.save
      render json: { filters_data: @quick_view.filters_data.sort_by { |filter| filter['order'] } }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  private
  def set_quick_views_data(filters_data)
    if @quick_view.present?
      @quick_view.assign_attributes(filters_data: filters_data)
    else
      @quick_view = scoped_company_user.quick_view_filters.new(workspace_id: scoped_workspace.id, company_id: scoped_company.id, filters_data: filters_data)
    end
  end

  def fetch_tickets_count(filters)
    @data = { page: '1', per_page: '25', sort: 'ticket_number asc' }
    if (filters['is_default'])
      case filters['title']
      when 'My Active Tickets'
        @data[:my_tickets] = true
        @data[:statuses] = ['Active']
      when 'Unassigned'
        @data[:statuses] = ['Active']
        @data[:assignment] = 'Unassigned'
      when 'All Active Tickets'
        @data[:statuses] = ["Active"]
      when 'Closed'
        @data[:statuses] = ['Closed']
      when 'Drafts'
        @data[:statuses] = ['Drafts']
      end
    else
      filters['filters'].each do |filter|
        process_filter(filter)
      end
    end

    @base_ticket = HelpTickets::BaseTicket.new(scoped_company, scoped_company_user, current_user, scoped_workspace, @data, @data)
    count = total_ticket_count
    reset_data
    count
  end

  def process_filter(filter)
    label = filter['label']
    
    if ['Assigned to', 'Created by'].include?(label)
      filter['values'].each do |val|
        @data[FILTER_MAP[label][0]] ||= []
        @data[FILTER_MAP[label][1]] ||= []
        @data[FILTER_MAP[label][0]] << val['id']
        @data[FILTER_MAP[label][1]] << val['name']
      end
    elsif label == 'Timeframe'
      filter['values'].each do |val|
        if val['filter_type'] == 'custom_date'
          @data[:date_filter] = val['value']
          @data[:custom_date_filter_type] = {
            startDate: val['start_date'],
            endDate: val['end_date'],
            filter: 'custom_date',
            name: 'Custom date'
          }.to_json
        else
          @data[:date_filter] = val['value']
          @data[:filter_type] = val['filter_type']
        end
      end
    elsif label == 'Workspace'
      filter['values'].each do |val|
        @data[:workspace_id] = val['id']
      end
    elsif label.nil? && filter['method_name'] == 'setFilterByField'
      filter['values'].each do |val|
        @data[:filter_by_field] ||= []
        @data[:filter_by_field] << val.to_json
      end
    else
      filter['values'].each do |val|
        label ||= val['label']
        @data[FILTER_MAP[label]] ||= []
        @data[FILTER_MAP[label]] << val['value']
      end
    end
  end

  def reset_data
    @ticket_ids = nil
    @total_ticket_count = nil
    @query_params = nil
  end
end
