# frozen_string_literal: true

class Api::V1::Options::CreatedByOptionsController < Api::V1::Options::OptionsController
  def options
    debugger
    puts "=== CREATED BY OPTIONS DEBUG ==="
    puts "current_user: #{current_user&.id}"
    puts "scoped_company: #{scoped_company&.id}"
    puts "loose_scoping: #{loose_scoping}"
    puts "=== END CREATED BY OPTIONS DEBUG ==="

    ::Options::CreatedByOptions.call(scoped_company, params)
  end
end
