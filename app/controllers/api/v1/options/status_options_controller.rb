# frozen_string_literal: true

class Api::V1::Options::StatusOptionsController < Api::V1::Options::FieldDataController
  def fields
    debugger
    puts "=== STATUS OPTIONS DEBUG ==="
    puts "current_user: #{current_user&.id}"
    puts "scoped_company: #{scoped_company&.id}"
    puts "scoped_workspace: #{scoped_workspace&.id rescue 'ERROR'}"
    puts "loose_scoping: #{loose_scoping}"
    puts "=== END STATUS OPTIONS DEBUG ==="

    ::Options::StatusOptions.call(scoped_workspace)
  end
end
