class AuthenticatedController < ApplicationController
  include MultiCompany::GeneralScoping
  include ApplicationHelper

  before_action :skip_trackable # Must be kept above :authenticate_user!
  before_action :ensure_access
  before_action :ensure_company_subdomain
  protect_from_forgery with: :exception, prepend: true
  before_action :authenticate_user!
  before_action :verify_mfa
  before_action :ensure_free_trial_or_subscribed
  before_action :ensure_confirmed_email
  before_action :ensure_company_verification, unless: :super_admin?

  rescue_from Exceptions::AccessDenied, with: :handle_access_denied
  
  helper_method :all_expanded_privileges
  helper_method :has_write_company_permission?
  helper_method :has_read_permission?
  helper_method :current_workspace_session
  helper_method :workspaces_json
  helper_method :associated_company_ids

  protected
  def admin?
    AccessCheck.new(current_user, current_company).admin?
  end

  def super_admin?
    super_admin
  end

  def super_admin
    @super_admin = User.find_by_cache(guid: session[:super_admin_guid])
  end

  def ensure_company_subdomain
    if is_company_subdomain? && current_company.present? && subdomain != current_company.subdomain
      redirect_to no_access_url(host: company_host)
    end
  end

  def ensure_company_verification
    if current_company || current_user
      redirect_to review_companies_url unless is_company_verified?
    end
  end

  def is_company_verified?
    (current_company.present? && current_company.verified) || (current_user.present? && current_user.companies.not_sample.where(verified: true).present?)
  end

  def verify_mfa
    if current_company&.mfa_enabled && current_company_user&.mfa_verification_required? && !current_user.super_admin?
      redirect_url = request.path

      if request.path == "/confirm_email"
        redirect_url = "/confirm_email?confirmation_token=#{params["confirmation_token"]}"
      end

      redirect_to mfa_index_path(redirect_url: redirect_url)
    elsif current_user&.super_admin
      verify_super_admin_mfa
    end
  end

  def ensure_access
    unless is_secure_subdomain? || common?
      verb = is_secure_subdomain? ? 'is' : 'is NOT'
      logger.info("domain #{request.host} #{verb} a secure domain (#{root_domain}")
      verb = common? ? 'has' : 'does NOT have'
      msg = "user #{current_user} #{verb} common access to #{current_company&.subdomain}"
      logger.info(msg)
      store_path_in_cookies
      respond_to do |format|
        format.html { redirect_to no_access_url(host: secure_host, sub: subdomain, params: { redirection_path: request.env["REQUEST_PATH"] }) }
        format.json { render json: { message: "Sorry, you're not authorized to perform this action." }, status: :unauthorized }
      end
    end
  end

  def store_path_in_cookies
    path = request.path
    query_string = request.env["QUERY_STRING"]
    if query_string.present?
      cookies[:redirection_path] = "#{path}?#{query_string},#{current_company&.subdomain}"
    else
      cookies[:redirection_path] = "#{path},#{current_company&.subdomain}"
    end
  end

  def ensure_admin
    redirect_to no_access_path unless current_user.super_admin? || current_company_user.is_admin?
  end

  def ensure_company_access
    unless current_user.super_admin? || current_company_user.is_admin? || permissions['CompanyUser']['root'].include?('write')
      redirect_to no_access_path
    end
  end

  def skip_trackable
    if super_admin.present? && super_admin.super_admin?
      request.env["devise.skip_trackable"] = true
    end
  end

  def ensure_free_trial_or_subscribed
    return if current_company.blank? || current_user.blank?
    return if current_user.super_admin?
    return if current_company.has_current_free_trial? && current_company.subscriptions.count == 0
    return if !current_company.is_sample_company? && current_company.allow_access?(request.path)
    return if current_company.is_sample_company? && current_user.has_active_subscription? 
    return if is_active_legacy_company?

    if !current_company.is_sample_company? && !current_company.allow_access?(request.path)
      if !current_company.has_current_free_trial? && current_company.subscriptions.count == 0
        return redirect_to company_billing_pricing_path
      end
      return redirect_to '/unsubscribed'
    end

    respond_to do |format|
      format.json { render json: {}, status: :ok }
      format.html { redirect_to company_billing_pricing_path }
    end
  end

  def ensure_confirmed_email
    if current_user.present? && !current_user.has_confirmed_email? && current_user.created_at < (Rails.application.credentials.unconfirmed_email_days_allowed || 5).to_i.days.ago
      redirect_to email_confirmation_path
    end
  end

  def include_stripe
    @stripe_needed = true
  end

  def include_hotglue
    @hotglue_needed = true
  end

  def module_permissions
    permisions[company_module]
  end

  def permissions
    @permissions ||= current_user.super_admin? ? super_admin_permissions : Permissions::Mapper.new(scoped_company_user).call.permissions
  end

  def super_admin_permissions
    @super_admin_permissions ||= begin
      module_names = ['CompanyUser', 'Contract', 'TelecomService', 'ManagedAsset', 'Monitoring', 'Vendor', 'HelpTicket', 'MobileApp']
      super_admin_permissions = {}

      module_names.each do |module_name|
        super_admin_permissions[module_name] ||= {}

        if module_name == 'HelpTicket'
          scoped_company_user.company&.workspaces.find_each do |workspace|
            super_admin_permissions[module_name]["#{workspace.id}"] = ['write']
          end
        else
          super_admin_permissions[module_name]['root'] = ['write']
        end
      end
      super_admin_permissions
    end
  end

  private

  def handle_access_denied(exception)
    error_context = {
      user_id: current_user&.id,
      company_id: scoped_company&.id,
      workspace_id: params[:workspace_id] || request.headers["HTTP_X_GENUITY_WORKSPACE_ID"],
      privilege_name: respond_to?(:privilege_name) ? privilege_name : nil,
      request_path: request.path,
      request_method: request.method
    }

    Rails.logger.error "Access Denied: #{exception.message} - Context: #{error_context}"
    Bugsnag.notify(exception, error_context) if Rails.env.staging? || Rails.env.production?

    respond_to do |format|
      format.html { render 'layouts/access_denied' }
      format.json { render json: { message: "Sorry, you're not authorized to perform this action." }, status: :forbidden }
    end
  end

  def workspaces_json
    workspaces = Workspaces::Resolver.new(current_company_user).call.workspaces
    workspaces_array = workspaces.map do |w|
      {
        id: w.id,
        name: w.name,
        company_id: w.company_id,
      }
    end
    workspaces_array.to_json
  end

  def associated_company_ids
    @associated_company_ids ||= current_user.company_users.pluck(:company_id)
  end

  def associated_workspaces
    @workspaces ||= Workspaces::Resolver.new(current_company_user).call.workspaces
  end

  def current_contributor
    current_company_user.contributor if current_company_user
  end
end
