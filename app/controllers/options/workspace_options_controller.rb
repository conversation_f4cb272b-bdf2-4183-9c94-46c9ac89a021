module Options
  class WorkspaceOptionsController < Options::OptionsController
    skip_before_action :ensure_access
    skip_before_action :verify_mfa
    skip_before_action :ensure_free_trial_or_subscribed
    skip_before_action :ensure_confirmed_email
    skip_before_action :ensure_company_verification

    # Override loose_scoping to allow workspace options to be fetched without strict workspace context
    def loose_scoping
      true
    end

    private
    def options
      if is_cache_enabled? && validate_cache_key_requirements?
        cache_key = generate_cache_key(nil, scoped_company, false, is_help_desk_module?, "#{current_user&.super_admin?}_#{scoped_company_user&.id}")
        Rails.cache.fetch(cache_key, expires_in: 8.hours) do
          track_cached_keys(cache_key, 'workspace_options')
          get_workspace_options
        end
      else
        get_workspace_options
      end
    end

    def get_workspace_options
      ::Options::WorkspaceOptions.call(
        params,
        current_user,
        scoped_company,
        scoped_company_user,
        all_expanded_privileges
      )
    end

    def validate_cache_key_requirements?
      if scoped_company.blank?
        raise 'Scoped company is not present for cache key.'
      elsif params[:action].blank? || params[:controller].blank?
        raise 'Action/Controller information is missing in parameters for cache key.'
      end

      true
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      false
    end

    def privilege_name
      params[:privilege_name]
    end

    def permission_type
      params[:permission_type]
    end

    def ensure_privilege_name
      raise "privilege_name is required" unless privilege_name
    end
  end
end
